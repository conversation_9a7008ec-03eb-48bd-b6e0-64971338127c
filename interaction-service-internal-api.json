{"openapi": "3.0.3", "info": {"title": "Interaction Service Internal API", "description": "Internal API for service-to-service communication for interactions including favorites, likes, and play history", "version": "1.0.0", "contact": {"name": "PxPat Backend Team"}}, "servers": [{"url": "http://localhost:12009/internal/v1", "description": "Development server"}], "components": {"schemas": {"GlobalResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "Response code"}, "message": {"type": "string", "description": "Response message"}, "data": {"description": "Response data"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "Error code"}, "message": {"type": "string", "description": "Error message"}}}}}, "paths": {"/favorites/add": {"post": {"tags": ["Internal Favorites"], "summary": "Add to favorite (Internal)", "description": "Internal API to add content to favorite", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuid": {"type": "string"}, "content_type": {"type": "string", "enum": ["video", "anime", "short"]}, "folder_id": {"type": "string"}}, "required": ["user_ksuid", "content_ksuid", "content_type"]}}}}, "responses": {"200": {"description": "Added to favorite successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/favorites/remove": {"post": {"tags": ["Internal Favorites"], "summary": "Remove from favorite (Internal)", "description": "Internal API to remove content from favorite", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuid": {"type": "string"}, "folder_id": {"type": "string"}}, "required": ["user_ksuid", "content_ksuid"]}}}}, "responses": {"200": {"description": "Removed from favorite successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/check-status": {"post": {"tags": ["Internal Favorites"], "summary": "Check favorite status (Internal)", "description": "Internal API to check if content is favorited", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuid": {"type": "string"}}, "required": ["user_ksuid", "content_ksuid"]}}}}, "responses": {"200": {"description": "Favorite status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/batch-check-status": {"post": {"tags": ["Internal Favorites"], "summary": "Batch check favorite status (Internal)", "description": "Internal API to batch check favorite status", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_ksuid": {"type": "string"}, "content_ksuids": {"type": "array", "items": {"type": "string"}}}, "required": ["user_ksuid", "content_ksuids"]}}}}, "responses": {"200": {"description": "Batch favorite status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}, "/favorites/stats/{user_ksuid}": {"get": {"tags": ["Internal Favorites"], "summary": "Get user favorite stats (Internal)", "description": "Internal API to get user's favorite statistics", "parameters": [{"name": "user_ksuid", "in": "path", "required": true, "schema": {"type": "string"}, "description": "User KSUID"}], "responses": {"200": {"description": "User favorite statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlobalResponse"}}}}}}}}}